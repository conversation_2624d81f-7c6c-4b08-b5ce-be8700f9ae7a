<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deviceTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="点位类型" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </template>
        </time-analysis-selector>

        <!--   图表数据    -->
        <el-row :gutter="20">
          <!-- 能耗趋势柱状图 -->
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">能耗趋势分析</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('energyBarChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="energyBarChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 设备能耗对比图 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">设备能耗对比</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDeviceDataView"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('deviceCompareChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="deviceCompareChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 数据视图对话框 -->
        <el-dialog :title="dataViewTitle" :visible.sync="dataViewVisible" width="50%">
          <!-- 时间序列数据视图 -->
          <el-table v-if="dataViewType === 'time'" :data="dataViewData" border style="width: 100%">
            <el-table-column prop="date" label="日期" width="120"></el-table-column>
            <el-table-column v-for="device in analyseData" :key="device.deviceId" :label="device.deviceName">
              <template slot-scope="scope">
                {{ scope.row.devices[device.deviceId] ? scope.row.devices[device.deviceId].value.toFixed(2) : '0.00' }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 设备对比数据视图 -->
          <el-table v-else :data="dataViewData" border style="width: 100%">
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="value" label="总能耗值">
              <template slot-scope="scope">
                {{ scope.row.value.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
      </el-col>

    </el-row>
  </div>
</template>

<script>

import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {deviceTree} from "@/api/biz/device";
import {energyAnalyse} from "@/api/biz/deviceAnalyse";
import * as echarts from 'echarts';

export default {
  name: 'energyEfficiencyAnalyse',
  components: {TimeAnalysisSelector},
  dicts: ['point_type'],
  data() {
    return {
      deviceTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,
      energyType: '1',

      // 查询参数
      queryParams: {
        deviceIds: [],
        analysisType: undefined,
        pointType: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 遮罩层
      loading: false,

      // 图表实例
      energyBarChart: null,
      deviceCompareChart: null,

      // 分析数据
      analyseData: [],

      // 数据视图
      dataViewVisible: false,
      dataViewTitle: '能耗数据详情',
      dataViewData: [],
      dataViewType: 'time', // 'time' 或 'device'

      // 图表颜色
      chartColors: [
        '#5470C6', '#91CC75', '#FAC858', '#EE6666',
        '#73C0DE', '#3BA272', '#FC8452', '#9A60B4',
        '#EA7CCC', '#FF9F7F', '#FFDB5C', '#9FE6B8'
      ]
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDeviceTree()
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeCharts)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    // 能耗分析
    getEnergyAnalyse(){
      this.loading = true
      energyAnalyse(this.queryParams).then(res => {
        this.analyseData = res.data || []
        this.loading = false
        this.initCharts()
        this.prepareDataViewData()
      }).catch(error => {
        console.error('获取能耗分析数据失败:', error)
        this.$message.error('获取能耗分析数据失败')
        this.loading = false
      })
    },

    // 初始化所有图表
    initCharts() {
      this.initEnergyBarChart()
      this.initDeviceCompareChart()
    },

    // 初始化能耗趋势柱状图
    initEnergyBarChart() {
      // 销毁旧图表
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
      }

      const chartDom = this.$refs.energyBarChart
      if (!chartDom) return

      this.energyBarChart = echarts.init(chartDom, 'macarons')

      // 准备数据
      const timeData = this.prepareTimeSeriesData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin: 3px 0">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: ${param.value.toFixed(2)}
              </div>`
            }

            return result
          }
        },
        legend: {
          data: timeData.legendData,
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: timeData.times,
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return '';

              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  // 如果是无效日期，直接返回原始值
                  return value;
                }

                // 根据日期字符串长度判断数据类型
                if (value.includes('-') || value.includes('/')) {
                  // 包含日期分隔符的情况
                  if (value.includes(':') || value.includes(' ')) {
                    // 小时格式 (HOURLY)
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    // 月份格式 (MONTHLY)
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    // 日期格式 (DAILY)
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  // 年份格式 (YEARLY)
                  return value;
                } else {
                  // 周格式 (WEEK) 或其他
                  return value;
                }
              } catch (e) {
                // 发生异常时返回原始值
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '能耗值',
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: timeData.series
      }

      this.energyBarChart.setOption(option)
    },

    // 初始化设备能耗对比图
    initDeviceCompareChart() {
      // 销毁旧图表
      if (this.deviceCompareChart) {
        this.deviceCompareChart.dispose()
      }

      const chartDom = this.$refs.deviceCompareChart
      if (!chartDom) return

      this.deviceCompareChart = echarts.init(chartDom, 'macarons')

      // 准备数据
      const deviceData = this.prepareDeviceCompareData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin-top:5px">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: ${param.value.toFixed(2)}
              </div>`
            }

            return result
          }
        },
        legend: {
          data: deviceData.legendData,
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: deviceData.times,
          boundaryGap: true,
          axisLabel: {
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '能耗值',
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: deviceData.series
      }

      this.deviceCompareChart.setOption(option)
    },

    // 准备时间序列数据
    prepareTimeSeriesData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { times: [], legendData: [], series: [] }
      }

      // 使用第一个设备的时间作为X轴
      const times = this.analyseData[0].analyseData.times || []
      const legendData = this.analyseData.map(item => item.deviceName)

      // 为每个设备创建一个系列
      const series = this.analyseData.map((device, index) => {
        const color = this.chartColors[index % this.chartColors.length]
        return {
          name: device.deviceName,
          type: 'line',
          data: device.analyseData.values.map(val => parseFloat(val.toFixed(2))),
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: color
          },
          itemStyle: {
            color: color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: color,
              shadowOffsetY: 0,
              borderWidth: 3
            },
            lineStyle: {
              width: 4
            }
          },
          areaStyle: {
            opacity: 0.1,
            color: color
          }
        }
      })

      return { times, legendData, series }
    },

    // 准备设备对比数据
    prepareDeviceCompareData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { times: [], legendData: [], series: [] }
      }

      // 使用第一个设备的时间作为X轴
      const times = this.analyseData[0].analyseData.times || []
      const legendData = this.analyseData.map(item => item.deviceName)

      // 计算每个设备的总能耗
      const totalValues = this.analyseData.map(device => {
        const values = device.analyseData.values || []
        return values.reduce((sum, val) => sum + (val || 0), 0)
      })

      // 创建设备对比柱状图系列
      const series = [{
        name: '设备总能耗',
        type: 'bar',
        barWidth: '60%',
        data: this.analyseData.map((device, index) => {
          return {
            value: parseFloat(totalValues[index].toFixed(2)),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#83bff6'},
                {offset: 0.5, color: '#188df0'},
                {offset: 1, color: '#188df0'}
              ])
            }
          }
        }),
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {offset: 0, color: '#2378f7'},
              {offset: 0.7, color: '#2378f7'},
              {offset: 1, color: '#83bff6'}
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params) => {
            return `${params.value.toFixed(2)}`
          }
        },
        markLine: {
          symbol: 'none',
          lineStyle: {
            color: '#5470C6',
            type: 'dashed'
          },
          data: [
            {
              type: 'average',
              name: '平均值',
              label: {
                position: 'middle',
                formatter: (params) => {
                  return `平均: ${params.value.toFixed(2)}`
                },
                fontSize: 10,
                color: '#5470C6',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                padding: [2, 4],
                borderRadius: 2,
                distance: 10
              }
            }
          ]
        }
      }]

      return {
        times: legendData, // 使用设备名称作为X轴
        legendData: ['设备总能耗'],
        series: series
      }
    },

    // 准备数据视图数据
    prepareDataViewData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 默认准备时间序列数据视图
      this.prepareTimeDataView()
    },

    // 准备时间序列数据视图
    prepareTimeDataView() {
      this.dataViewType = 'time'
      this.dataViewTitle = '能耗数据详情'

      const firstDevice = this.analyseData[0]
      if (!firstDevice) {
        this.dataViewData = []
        return
      }

      const times = firstDevice.analyseData.times || []

      // 为每个时间点创建一行数据
      this.dataViewData = times.map((time, timeIndex) => {
        const rowData = {
          date: time,
          devices: {}
        }

        // 添加每个设备在该时间点的能耗值
        this.analyseData.forEach(device => {
          const value = device.analyseData.values[timeIndex] || 0
          rowData.devices[device.deviceId] = {
            deviceName: device.deviceName,
            value: parseFloat(value.toFixed(2))
          }
        })

        return rowData
      })
    },

    // 准备设备对比数据视图
    prepareDeviceDataView() {
      this.dataViewType = 'device'
      this.dataViewTitle = '设备能耗对比'

      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 计算每个设备的总能耗
      this.dataViewData = this.analyseData.map(device => {
        const values = device.analyseData.values || []
        const totalValue = values.reduce((sum, val) => sum + (val || 0), 0)

        return {
          deviceName: device.deviceName,
          deviceId: device.deviceId,
          value: parseFloat(totalValue.toFixed(2)),
          unit: 'kWh'
        }
      })
    },

    // 显示数据视图
    showDataView() {
      this.prepareTimeDataView()
      this.dataViewVisible = true
    },

    // 显示设备数据视图
    showDeviceDataView() {
      this.prepareDeviceDataView()
      this.dataViewVisible = true
    },

    // 保存图表为图片
    saveAsImage(chartRef) {
      const chart = this[chartRef]
      if (!chart) return

      const url = chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.download = '能耗分析_' + new Date().getTime() + '.png'
      link.href = url
      link.click()
    },

    // 调整图表大小
    resizeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.resize()
      }
      if (this.deviceCompareChart) {
        this.deviceCompareChart.resize()
      }
    },

    // 销毁图表
    disposeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
        this.energyBarChart = null
      }
      if (this.deviceCompareChart) {
        this.deviceCompareChart.dispose()
        this.deviceCompareChart = null
      }
    },
    // 获取区域设备树结构
    getDeviceTree() {
      this.typeLoading = true
      deviceTree().then(res => {
        this.deviceTreeOptions = res.data
        this.typeLoading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.deviceIds = []

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.getEnergyAnalyse()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.analysisType || !this.queryParams.startTime ||
        !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择设备、点位、分析方式和时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "device" 的节点 ID
      this.queryParams.deviceIds = checkedNodes
        .filter(node => node.type === 'device')
        .map(node => node.id);

      // 检查是否选择了设备
      if (this.queryParams.deviceIds.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return false;
      }
      return true;
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },
  }
}
</script>

<style scoped>
.chart-container {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tools i {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-tools i:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 280px;
  position: relative;
}
</style>
